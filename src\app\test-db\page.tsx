// src/app/test-db/page.tsx
'use client';

import { useState } from 'react';
import { seedAllMasterData } from '../../utils/database-seeder';
import { runDatabaseVerification, testMasterDataRetrieval, verifyTablesExist } from '../../utils/database-test';

export default function DatabaseTestPage() {
  const [results, setResults] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [seeding, setSeeding] = useState(false);

  const runTests = async () => {
    setLoading(true);
    setResults(null);
    
    try {
      console.log('🚀 Running database tests...');
      
      // Run verification
      await runDatabaseVerification();
      
      // Get detailed results
      const tableVerification = await verifyTablesExist();
      const dataRetrieval = await testMasterDataRetrieval();
      
      setResults({
        tableVerification,
        dataRetrieval,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Test error:', error);
      setResults({
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      });
    } finally {
      setLoading(false);
    }
  };

  const runSeeding = async () => {
    setSeeding(true);

    try {
      console.log('🌱 Running database seeding...');
      const seedResults = await seedAllMasterData();

      // Refresh test results after seeding
      if (seedResults.success) {
        await runTests();
      }
    } catch (error) {
      console.error('Seeding error:', error);
    } finally {
      setSeeding(false);
    }
  };

  return (
    <div className="min-h-screen bg-slate-50 p-8">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white rounded-lg shadow-sm border border-slate-200 p-6">
          <h1 className="text-2xl font-bold text-slate-900 mb-6">Database Verification Test</h1>
          
          <div className="mb-6 flex gap-4">
            <button
              onClick={runTests}
              disabled={loading || seeding}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? 'Running Tests...' : 'Run Database Tests'}
            </button>

            <button
              onClick={runSeeding}
              disabled={loading || seeding}
              className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {seeding ? 'Seeding Database...' : 'Seed Sample Data'}
            </button>
          </div>

          {results && (
            <div className="space-y-6">
              <div className="bg-slate-50 rounded-lg p-4">
                <h2 className="text-lg font-semibold text-slate-900 mb-3">Test Results</h2>
                <p className="text-sm text-slate-600 mb-4">
                  Timestamp: {new Date(results.timestamp).toLocaleString()}
                </p>

                {results.error ? (
                  <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                    <h3 className="text-red-800 font-medium">Error</h3>
                    <p className="text-red-700">{results.error}</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {/* Table Verification */}
                    <div className="bg-white rounded-lg border border-slate-200 p-4">
                      <h3 className="font-medium text-slate-900 mb-3">Table Verification</h3>
                      
                      {results.tableVerification.exists.length > 0 && (
                        <div className="mb-3">
                          <h4 className="text-sm font-medium text-green-800 mb-2">✅ Existing Tables</h4>
                          <ul className="text-sm text-green-700 space-y-1">
                            {results.tableVerification.exists.map((table: string) => (
                              <li key={table} className="flex items-center">
                                <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                                {table}
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}

                      {results.tableVerification.missing.length > 0 && (
                        <div>
                          <h4 className="text-sm font-medium text-red-800 mb-2">❌ Missing Tables</h4>
                          <ul className="text-sm text-red-700 space-y-1">
                            {results.tableVerification.missing.map((table: string) => (
                              <li key={table} className="flex items-center">
                                <span className="w-2 h-2 bg-red-500 rounded-full mr-2"></span>
                                {table}
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>

                    {/* Data Retrieval */}
                    <div className="bg-white rounded-lg border border-slate-200 p-4">
                      <h3 className="font-medium text-slate-900 mb-3">Data Retrieval Test</h3>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                        {Object.entries(results.dataRetrieval).map(([table, count]) => (
                          <div key={table} className="text-center">
                            <div className={`text-2xl font-bold ${count > 0 ? 'text-green-600' : 'text-orange-600'}`}>
                              {count}
                            </div>
                            <div className="text-sm text-slate-600 capitalize">
                              {table.replace(/([A-Z])/g, ' $1').trim()}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Recommendations */}
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                      <h3 className="font-medium text-blue-900 mb-2">💡 Recommendations</h3>
                      <ul className="text-sm text-blue-800 space-y-1">
                        {results.tableVerification.missing.length > 0 && (
                          <li>• Create missing tables or verify table names in DATABASE_TABLES constant</li>
                        )}
                        {Object.values(results.dataRetrieval).every((count: any) => count === 0) && (
                          <li>• Tables exist but contain no data. Consider seeding the database with sample data.</li>
                        )}
                        {Object.values(results.dataRetrieval).some((count: any) => count > 0) && (
                          <li>• ✅ Database is properly configured and contains data!</li>
                        )}
                      </ul>
                    </div>
                  </div>
                )}
              </div>

              {/* Console Output Notice */}
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <h3 className="font-medium text-yellow-900 mb-2">📝 Console Output</h3>
                <p className="text-sm text-yellow-800">
                  Detailed logs and additional information are available in the browser console. 
                  Open Developer Tools (F12) and check the Console tab for comprehensive test results.
                </p>
              </div>
            </div>
          )}

          {/* Instructions */}
          <div className="mt-8 bg-slate-50 rounded-lg p-4">
            <h3 className="font-medium text-slate-900 mb-2">How to Use This Test</h3>
            <ol className="text-sm text-slate-700 space-y-1 list-decimal list-inside">
              <li>Click "Run Database Tests" to verify your Supabase connection and table structure</li>
              <li>Check the results above to see which tables exist and contain data</li>
              <li>Open browser console (F12) for detailed logs and error messages</li>
              <li>Follow the recommendations to fix any issues</li>
              <li>Re-run tests after making changes to verify fixes</li>
            </ol>
          </div>
        </div>
      </div>
    </div>
  );
}
