// src/lib/auth.ts
'use client';

export interface User {
  name: string;
  email: string;
  role: string;
  isAuthenticated: boolean;
}

export const getAuthState = async (): Promise<User | null> => {
  // Check session-based authentication
  try {
    const { verifySession } = await import('./session');
    return await verifySession();
  } catch (error) {
    console.error('Failed to get auth state:', error);
    return null;
  }
};

export const logout = async () => {
  if (typeof window !== 'undefined') {
    try {
      // Sign out from Supabase
      const { signOut } = await import('./supabase-auth-functions');
      await signOut();

      // Delete session
      const { deleteSession } = await import('./session');
      await deleteSession();

      // Redirect to auth page instead of product page
      window.location.href = '/auth';
    } catch (error) {
      console.error('Logout error:', error);
      // Force redirect even if logout fails
      window.location.href = '/auth';
    }
  }
};

export const requireAuth = async () => {
  if (typeof window !== 'undefined') {
    // Check session-based authentication first
    try {
      const { verifySession } = await import('./session');
      const sessionUser = await verifySession();
      if (sessionUser) {
        return true;
      }

      // Fallback to Supabase check
      const { getCurrentUser } = await import('./supabase-auth-functions');
      const currentUser = await getCurrentUser();
      if (!currentUser) {
        // Redirect to auth page instead of product page
        window.location.href = '/auth';
        return false;
      }
      return true;
    } catch (error) {
      console.error('Auth verification failed:', error);
      window.location.href = '/auth';
      return false;
    }
  }
  return false;
};

// Initialize auth state from session or Supabase
export const initializeAuth = async (): Promise<User | null> => {
  if (typeof window === 'undefined') {
    return null;
  }

  try {
    // Check session first
    const { verifySession } = await import('./session');
    const sessionUser = await verifySession();
    if (sessionUser) {
      return sessionUser;
    }

    // Fallback to Supabase
    const { getCurrentUser } = await import('./supabase-auth-functions');
    const currentUser = await getCurrentUser();
    return currentUser;
  } catch (error) {
    console.error('Failed to initialize auth:', error);
    return null;
  }
};
